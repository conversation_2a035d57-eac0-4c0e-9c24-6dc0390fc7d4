
import sys
sys.path.insert(0, '.')
import argparse
import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from PIL import Image
import numpy as np
import cv2
import onnx

import lib.data.transform_cv2 as T
from lib.models import model_factory
from configs import set_cfg_from_file


# uncomment the following line if you want to reduce cpu usage, see issue #231
#  torch.set_num_threads(4)

torch.set_grad_enabled(False)
np.random.seed(123)


# args
parse = argparse.ArgumentParser()
parse.add_argument('--config', dest='config', type=str, default='configs/bisenetv2.py',)
parse.add_argument('--weight-path', type=str, default='./res/model_final.pth',)
parse.add_argument('--img-path', dest='img_path', type=str, default='./example.png',)
args = parse.parse_args()
cfg = set_cfg_from_file(args.config)


palette = np.random.randint(0, 256, (256, 3), dtype=np.uint8) # 随机生成颜色调色板

# define model
net = model_factory[cfg.model_type](cfg.n_cats, aux_mode='eval')# 根据配置创建模型实例
net.load_state_dict(torch.load(args.weight_path, map_location='cpu'), strict=False)
net.eval()# 将模型设置为评估模式
net.cuda()# 将模型移动到GPU

# prepare data # 创建ToTensor对象，用于图像预处理
to_tensor = T.ToTensor(
    mean=(0.3257, 0.3690, 0.3223), # city, rgb
    std=(0.2112, 0.2148, 0.2115),
)
im = cv2.imread(args.img_path)[:, :, ::-1]
im = to_tensor(dict(im=im, lb=None))['im'].unsqueeze(0).cuda()# 预处理图像并移动到GPU

# shape divisor
org_size = im.size()[2:]
print(f"org_size尺寸：{org_size}") # [1000, 1000]
new_size = [math.ceil(el / 32) * 32 for el in im.size()[2:]]
print(f"new_size尺寸：{new_size}") # 1024* 1024
print(f"im图像尺寸：{im.shape}") #[1, 3, 1000, 1000]
# inference
im = F.interpolate(im, size=new_size, align_corners=False, mode='bilinear')
print(f"模型输入图像尺寸：{im.shape}") #[1, 3, 1024, 1024]
out = net(im)[0]
out = F.interpolate(out, size=org_size, align_corners=False, mode='bilinear')
print(f"模型输出图像尺寸1：{out.shape}")#[1, 8, 1000, 1000]
out = out.argmax(dim=1)
print(f"模型输出图像尺寸2：{out.shape}")#[1, 1000, 1000]
# visualize
out = out.squeeze().detach().cpu().numpy()
print(f"模型输出图像尺寸3：{out.shape}")#(1000, 1000)
pred = palette[out]
# cv2.imwrite('./res.jpg', pred)

onnx_path = './bisenet.onnx'
torch.onnx.export(
    net,
    im,
    onnx_path,
    export_params=True,
    opset_version=11,
    do_constant_folding=True,
    input_names=['input'],
    output_names=['output'],
    dynamic_axes={
        'input': {0: 'batch_size'},
        'output': {0: 'batch_size'},
    }
)

print(f"✅ 模型已成功转换为 {onnx_path}")

# 检查 ONNX 模型是否有效
onnx_model = onnx.load("./bisenet.onnx")
onnx.checker.check_model(onnx_model)
print(f"✅ ONNX 模型有效 {onnx_path}")

# 验证ONNX精度
import onnxruntime as ort

# 原始框架（PyTorch）的输出
with torch.no_grad():
    out = net(im)[0]
    pytorch_output = out.cpu().numpy()

# ONNX 运行时的输出
ort_session = ort.InferenceSession("./bisenet.onnx")
onnx_output = ort_session.run(
    output_names=["output"],
    input_feed={"input": im.cpu().numpy()}
)[0]

# 对比输出是否一致
np.testing.assert_allclose(
    pytorch_output, onnx_output,
    rtol=1e-03, atol=1e-05  # 设置容忍误差
)
print(f"✅ 输出一致！")

# 将 onnx_output 从 NumPy 数组转换为 PyTorch 张量
onnx_output = torch.from_numpy(onnx_output)
print(f"onnx_output输出图像尺寸1：{onnx_output.shape}")#(1, 8, 1024, 1024)
onnx_output = F.interpolate(onnx_output, size=org_size, align_corners=False, mode='bilinear')
onnx_output = np.argmax(onnx_output, axis=1) 
print(f"onnx_output输出图像尺寸2：{onnx_output.shape}")#(1, 1000, 1000)
onnx_output = onnx_output.squeeze().detach().cpu().numpy()
print(f"模型输出图像尺寸3：{onnx_output.shape}")#(1000, 1000)
pred = palette[onnx_output]
cv2.imwrite('./res_onnx.jpg', pred)
print(f"✅ saved result to ./res_onnx.jpg")

