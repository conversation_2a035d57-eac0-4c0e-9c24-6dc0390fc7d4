cfg = dict(
    model_type='bisenetv1',
    n_cats=8,
    num_aux_heads=2,
    lr_start=1e-2,
    weight_decay=5e-4,
    warmup_iters=300,
    max_iter=3000,
    dataset='SUPS',
    im_root='./datasets/SUPS',
    train_im_anns='./datasets/SUPS/train.csv',
    val_im_anns='./datasets/SUPS/val.csv',
    scales=[0.75, 2.],
    cropsize=[1024, 1024],
    eval_crop=[1024, 1024],
    eval_scales=[0.5, 0.75, 1.0, 1.25, 1.5, 1.75],
    ims_per_gpu=16,
    eval_ims_per_gpu=2,
    use_fp16=True,
    use_sync_bn=False,
    respth='./res',
)
