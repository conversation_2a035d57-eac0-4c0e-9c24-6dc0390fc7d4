#!/usr/bin/python
# -*- encoding: utf-8 -*-


import torch
import torch.nn as nn
import torch.nn.functional as F


#  import ohem_cpp
#  class OhemCELoss(nn.Module):
#
#      def __init__(self, thresh, lb_ignore=255):
#          super(Oh<PERSON><PERSON><PERSON>oss, self).__init__()
#          self.score_thresh = thresh
#          self.lb_ignore = lb_ignore
#          self.criteria = nn.CrossEntropyLoss(ignore_index=lb_ignore, reduction='mean')
#
#      def forward(self, logits, labels):
#          n_min = labels[labels != self.lb_ignore].numel() // 16
#          labels = ohem_cpp.score_ohem_label(
#                  logits, labels, self.lb_ignore, self.score_thresh, n_min).detach()
#          loss = self.criteria(logits, labels)
#          return loss

# 交叉熵损失类
class OhemCELoss(nn.Module):

    def __init__(self, thresh, lb_ignore=255):
        super(OhemCELoss, self).__init__()
        self.thresh = -torch.log(torch.tensor(thresh, requires_grad=False, dtype=torch.float)).cuda() #阈值，用于筛选难样本
        self.lb_ignore = lb_ignore #忽略的标签值
        self.criteria = nn.CrossEntropyLoss(ignore_index=lb_ignore, reduction='none') # 交叉熵损失函数，设置为不计算忽略标签且不进行损失值汇总

    def forward(self, logits, labels):
        n_min = labels[labels != self.lb_ignore].numel() // 16 # 计算游标标签（非忽略标签）的最小样本数，为有效标签数的1/16
        loss = self.criteria(logits, labels).view(-1) #计算所有样本的损失值
        # 如果样本数量不足，则从所有样本中选择损失值最大的n_min个样本作为难样本
        loss_hard = loss[loss > self.thresh]
        if loss_hard.numel() < n_min:
            loss_hard, _ = loss.topk(n_min)
        return torch.mean(loss_hard) # 返回难样本的平均损失值


if __name__ == '__main__':
    pass

