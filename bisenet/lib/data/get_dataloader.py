
import torch
from torch.utils.data import Dataset, DataLoader
import torch.distributed as dist

import lib.data.transform_cv2 as T
from lib.data.sampler import RepeatedDistSampler

from lib.data.SUPS_cv2 import SUPS





def get_data_loader(cfg, mode='train'):
    if mode == 'train':
        # 使用训练数据增加，设置批量大小，标注路径，并打乱数据
        trans_func = T.TransformationTrain(cfg.scales, cfg.cropsize)
        batchsize = cfg.ims_per_gpu
        annpath = cfg.train_im_anns
        shuffle = True
        drop_last = True
    elif mode == 'val':
        # 使用验证数据增强，设置批量大小，标注路径，不打乱数据
        trans_func = T.TransformationVal()
        batchsize = cfg.eval_ims_per_gpu
        annpath = cfg.val_im_anns
        shuffle = False
        drop_last = False
    # 动态创建数据集对象，根据配置中的数据集名称
    ds = eval(cfg.dataset)(cfg.im_root, annpath, trans_func=trans_func, mode=mode)

    if dist.is_initialized():
        # 如果分布式训练已经初始化，使用分布式采样器
        assert dist.is_available(), "dist should be initialzed"
        if mode == 'train':
            assert not cfg.max_iter is None
            # 计算训练图像总数：每个GPU图像数*GPU总数*最大迭代次数
            n_train_imgs = cfg.ims_per_gpu * dist.get_world_size() * cfg.max_iter
            sampler = RepeatedDistSampler(ds, n_train_imgs, shuffle=shuffle)
        else:
            # 验证模式下使用标准的分布式 采样器
            sampler = torch.utils.data.distributed.DistributedSampler(
                ds, shuffle=shuffle)
        # 创建批次采样器，用于将采样器与批量大小结合
        batchsampler = torch.utils.data.sampler.BatchSampler(
            sampler, batchsize, drop_last=drop_last
        )
        # 创建数据加载器，使用批次采样器，设置4个工作线程并启用内存锁定
        dl = DataLoader(
            ds,
            batch_sampler=batchsampler,
            num_workers=4,
            pin_memory=True,
        )
    else:
        # 非分布式训练，直接使用标准数据加载器
        dl = DataLoader(
            ds,
            batch_size=batchsize,
            shuffle=shuffle,
            drop_last=drop_last,
            num_workers=4,
            pin_memory=True,
        )
    return dl
