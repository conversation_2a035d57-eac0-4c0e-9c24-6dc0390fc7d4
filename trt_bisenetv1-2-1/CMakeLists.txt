cmake_minimum_required(VERSION 2.8.4)
project(bisenet)

# 查找OpenCV
find_package(OpenCV REQUIRED)

# 查找CUDA
find_package(CUDA REQUIRED)

# TensorRT头文件和库路径（如有不同请手动修改）
include_directories(/usr/include /usr/local/cuda/include /usr/local/include)
link_directories(/usr/lib /usr/local/cuda/lib64 /usr/local/lib /usr/lib/x86_64-linux-gnu)

# 项目头文件
include_directories(${OpenCV_INCLUDE_DIRS} ./src)

# 源文件
file(GLOB_RECURSE SRC_FILES
    src/*.cpp
    src/*.cu
)

# 编译可执行文件
cuda_add_executable(bisenet ${SRC_FILES})

# 链接OpenCV、CUDA、TensorRT
# TensorRT 7.x 主要库：nvinfer, nvonnxparser, nvparsers
# CUDA 主要库：cudart
# OpenCV

target_link_libraries(bisenet
    ${OpenCV_LIBS}
    nvinfer
    nvonnxparser
    cudart
)

