#include "trt_bisenet.h"
#include "mat_transform.hpp"
#include "gpu_func.cuh"

BiSeNet::BiSeNet(const OnnxInitParam& params) : TRTOnnxBase(params)
{
}

cv::Mat BiSeNet::Extract(const cv::Mat& img)
{
	if (img.empty())
		return img;

	std::lock_guard<std::mutex> lock(mtx_);
	/*PreProcessCpu(img);*/
	ProProcessGPU(img);
	Forward();

	/*cv::Mat res = PostProcessCpu();*/
	cv::Mat res = PostProcessGpu();
	return std::move(res);
}

BiSeNet::~BiSeNet()
{
}

void BiSeNet::PreProcessCpu(const cv::Mat& img)
{
	cv::Mat img_tmp = img;

	ComposeMatLambda compose(std::vector<ComposeMatLambda::FuncionType>{
		LetterResize(cv::Size(crop_size_, crop_size_), cv::<PERSON>ala<PERSON>(114, 114, 114), 32),
		MatDivConstant(255),
		MatNormalize(mean_, std_),
		});

	cv::Mat sample_float = compose(img_tmp);
	input_shape_.Reshape(1, sample_float.channels(), sample_float.rows, sample_float.cols);
	output_shape_.Reshape(1, _params.num_classes, sample_float.rows, sample_float.cols);

	Tensor2VecMat tensor_2_mat;
	std::vector<cv::Mat> channels = tensor_2_mat(h_input_tensor_, input_shape_);
	cv::split(sample_float, channels);

	cudaMemcpy(d_input_tensor_, h_input_tensor_, input_shape_.count() * sizeof(float),
				cudaMemcpyHostToDevice);
}


void BiSeNet::ProProcessGPU(const cv::Mat& img)
{
	int src_h = img.rows;
	int src_w = img.cols;
	int channels = img.channels();

	float r = MIN(float(crop_size_) / src_h, float(crop_size_) / src_w);

	int dst_h = int(r * src_h);
	int dst_w = int(r * src_w);

	int pad_h = (crop_size_ - dst_h) % stride_;
	int pad_w = (crop_size_ - dst_w) % stride_;

	dst_h += pad_h;
	dst_w += pad_w;

	input_shape_.Reshape(1, channels, dst_h, dst_w);
	output_shape_.Reshape(1, _params.num_classes, dst_h, dst_w);

	biliresize_normalize(d_input_tensor_, channels, src_h, src_w, dst_h, dst_w,
		pad_h, pad_w, r, img.data, mean_.data(), std_.data());
}

cv::Mat BiSeNet::PostProcessCpu()
{
	int num = output_shape_.num();
	int channels = output_shape_.channels();
	int height = output_shape_.height();
	int width = output_shape_.width();
	int count = output_shape_.count();

	cudaMemcpy(h_output_tensor_, d_output_tensor_,
		count * sizeof(float), cudaMemcpyDeviceToHost);

	cv::Mat res = cv::Mat::zeros(height, width, CV_8UC1);
	for (int row = 0; row < height; row++)
	{
		for (int col = 0; col < width; col++)
		{
			vector<float> vec;
			for (int c = 0; c < channels; c++)
			{
				int index = row * width + col + c * height * width;
				float val = h_output_tensor_[index];
				vec.push_back(val);
			}

			int idx = findMaxIdx(vec);
			if (idx == -1)
				continue;
			res.at<uchar>(row, col) = uchar(idx);
		}
	}

	return std::move(res);
}

cv::Mat BiSeNet::PostProcessGpu()
{
	int num = output_shape_.num();
	int channels = output_shape_.channels();
	int height = output_shape_.height();
	int width = output_shape_.width();

	std::cout << "PostProcessGpu - Output shape: " << num << "," << channels << "," << height << "," << width << std::endl;

	unsigned char* cpu_dst;
	cudaHostAlloc((void**)&cpu_dst, height * width * sizeof(unsigned char), cudaHostAllocDefault);

	// 检查CUDA错误
	cudaError_t err = cudaGetLastError();
	if (err != cudaSuccess) {
		std::cerr << "CUDA Error after cudaHostAlloc: " << cudaGetErrorString(err) << std::endl;
	}

	//==> segmentation(output_tensor_, channels, height, width, cpu_dst);
	segmentation(d_output_tensor_, channels, height, width, cpu_dst);

	// 检查CUDA错误
	err = cudaGetLastError();
	if (err != cudaSuccess) {
		std::cerr << "CUDA Error after segmentation: " << cudaGetErrorString(err) << std::endl;
	}

	// 创建一个新的Mat并复制数据，而不是使用外部数据
	cv::Mat res(height, width, CV_8UC1);
	memcpy(res.data, cpu_dst, height * width * sizeof(unsigned char));

	cudaFreeHost(cpu_dst);
	return res;
}

void BiSeNet::softmax(vector<float>& vec)
{
	float tol = 0.0;
	for (int i = 0; i < vec.size(); i++)
	{
		vec[i] = exp(vec[i]);
		tol += vec[i];
	}

	for (int i = 0; i < vec.size(); i++)
		vec[i] = vec[i] / tol;
}

int BiSeNet::findMaxIdx(const vector<float>& vec)
{
	if (vec.empty())
		return -1;
	auto pos = max_element(vec.begin(), vec.end());
	return std::distance(vec.begin(), pos);
}

cv::Mat BiSeNet::VisualizeSegmentation(const cv::Mat& segmentation, const cv::Mat& original_img)
{
	// 定义类别颜色映射（最多支持10个类别，可以根据需要扩展）
	std::vector<cv::Vec3b> colors = {
		cv::Vec3b(0, 0, 0),       // 类别0: 黑色 (背景)
		cv::Vec3b(128, 0, 0),     // 类别1: 深红色
		cv::Vec3b(0, 128, 0),     // 类别2: 深绿色
		cv::Vec3b(128, 128, 0),   // 类别3: 深黄色
		cv::Vec3b(0, 0, 128),     // 类别4: 深蓝色
		cv::Vec3b(128, 0, 128),   // 类别5: 深紫色
		cv::Vec3b(0, 128, 128),   // 类别6: 深青色
		cv::Vec3b(128, 128, 128), // 类别7: 灰色
		cv::Vec3b(64, 0, 0),      // 类别8: 暗红色
		cv::Vec3b(192, 0, 0)      // 类别9: 亮红色
	};

	// 创建彩色分割图
	cv::Mat colored_segmentation = cv::Mat::zeros(segmentation.size(), CV_8UC3);

	// 为每个像素分配颜色
	for (int y = 0; y < segmentation.rows; y++) {
		for (int x = 0; x < segmentation.cols; x++) {
			unsigned char class_id = segmentation.at<unsigned char>(y, x);
			// 确保类别索引在颜色数组范围内
			if (class_id < colors.size()) {
				colored_segmentation.at<cv::Vec3b>(y, x) = colors[class_id];
			}
		}
	}

	// 如果提供了原始图像，则将分割结果与原始图像混合
	if (!original_img.empty()) {
		cv::Mat resized_original;
		// 确保原始图像与分割结果大小相同
		cv::resize(original_img, resized_original, segmentation.size());

		// 混合原始图像和分割结果（半透明效果）
		cv::Mat blended;
		cv::addWeighted(resized_original, 0.7, colored_segmentation, 0.3, 0, blended);
		return blended;
	}

	return colored_segmentation;
}