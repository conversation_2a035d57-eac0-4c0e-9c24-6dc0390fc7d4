import onnx
from onnx import helper
from onnx import TensorProto
import numpy as np

# 创建输入输出
X = helper.make_tensor_value_info('input', TensorProto.FLOAT, [1, 3, 640, 640])
Y = helper.make_tensor_value_info('output', TensorProto.FLOAT, [1, 4, 640, 640])

# 创建节点
node_def = helper.make_node(
    'Conv',
    inputs=['input'],
    outputs=['output'],
    kernel_shape=[3, 3],
    pads=[1, 1, 1, 1],
    strides=[1, 1],
    dilations=[1, 1],
    group=1
)

# 创建权重
weight = np.random.randn(4, 3, 3, 3).astype(np.float32)
weight_tensor = helper.make_tensor(
    name='weight',
    data_type=TensorProto.FLOAT,
    dims=weight.shape,
    vals=weight.flatten().tolist()
)

# 创建图
graph_def = helper.make_graph(
    [node_def],
    'test-model',
    [X],
    [Y],
    [weight_tensor]
)

# 创建模型
model_def = helper.make_model(graph_def, producer_name='onnx-example')
model_def.opset_import[0].version = 11

# 保存模型
onnx.save(model_def, 'models/bisenetv3.onnx')
print("创建了占位符ONNX模型: models/bisenetv3.onnx")
