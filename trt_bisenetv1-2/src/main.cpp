/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-04-16 14:47:17
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-06-18 15:40:50
 * @FilePath: /Bisenet/trt_bisenetv1-2/src/main.cpp
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
#include "trt_bisenet.h"
#include <chrono>  // 用于性能测量
#include <random>   // 用于生成随机调色板

const float mean[] = {0.3257f, 0.3690f, 0.3223f}; // RGB 顺序，与Python版本一致
const float std_dev[]  = {0.2112f, 0.2148f, 0.2115f};

// 生成与Python版本相同的随机调色板
cv::Mat generatePythonStylePalette() {
	// 使用固定种子123，与Python版本保持一致
	std::mt19937 gen(123);
	std::uniform_int_distribution<int> dis(0, 255);

	cv::Mat palette(256, 1, CV_8UC3);
	for (int i = 0; i < 256; i++) {
		palette.at<cv::Vec3b>(i, 0) = cv::Vec3b(
			dis(gen), // B
			dis(gen), // G
			dis(gen)  // R
		);
	}
	return palette;
}

// 使用调色板对分割结果进行着色
cv::Mat applyPalette(const cv::Mat& segmentation, const cv::Mat& palette) {
	cv::Mat colored_result(segmentation.size(), CV_8UC3);

	for (int y = 0; y < segmentation.rows; y++) {
		for (int x = 0; x < segmentation.cols; x++) {
			uchar class_id = segmentation.at<uchar>(y, x);
			colored_result.at<cv::Vec3b>(y, x) = palette.at<cv::Vec3b>(class_id, 0);
		}
	}

	return colored_result;
}

int main(int argc, char** argv)
{
	OnnxInitParam params;
	params.onnx_model_path = "../models/bisenet.onnx";
	params.rt_stream_path = "../models/";
	params.rt_model_name = "bisenet.engine";
	params.use_fp16 = true;
	params.gpu_id = 0;
	params.num_classes = 8;
	params.crop_size = 1024;
	params.max_shape = Shape(1, 3, 1024, 1024); // 设置最大网络输入大小，用于分配内存(显存)，根据自己项目需要设置

	BiSeNet model(params);

	// 生成与Python版本相同的调色板
	cv::Mat palette = generatePythonStylePalette();

	// 读取图像
	cv::Mat img = cv::imread("../images/1647074048.342873-1.png", cv::IMREAD_COLOR);
	if (img.empty()) {
		std::cerr << "Error: Could not read image file!" << std::endl;
		return -1;
	}
	std::cout << "Image size: " << img.cols << "x" << img.rows << std::endl;



	std::cout << "Starting extraction..." << std::endl;
	try {
		// 运行语义分割
		cv::Mat segmentation = model.Extract(img);
		std::cout << "Extraction completed. Result size: " << segmentation.cols << "x" << segmentation.rows << std::endl;

		// 使用Python风格调色板生成彩色分割图
		cv::Mat python_style_result = applyPalette(segmentation, palette);

		// 保存Python风格的结果（与Python版本的res.jpg对应）
		std::string output_path = "../output/res.jpg";
		bool success = cv::imwrite(output_path, python_style_result);
		if (success) {
			std::cout << "Python-style result saved to " << output_path << std::endl;
		} else {
			std::cerr << "Failed to save Python-style result to " << output_path << std::endl;
		}

		// 将原始分割结果保存为图像文件
		output_path = "../output/output_segmentation.png";
		success = cv::imwrite(output_path, segmentation);
		if (success) {
			std::cout << "Raw segmentation result saved to " << output_path << std::endl;
		} else {
			std::cerr << "Failed to save raw segmentation result to " << output_path << std::endl;
		}

		// 生成彩色分割图（使用原有的可视化方法）
		cv::Mat colored_segmentation = model.VisualizeSegmentation(segmentation);
		output_path = "../output/output_colored_segmentation.png";
		success = cv::imwrite(output_path, colored_segmentation);
		if (success) {
			std::cout << "Colored segmentation saved to " << output_path << std::endl;
		} else {
			std::cerr << "Failed to save colored segmentation to " << output_path << std::endl;
		}

		// 测量性能：进行多次推理并计算平均时间
		const int num_iterations = 10;
		std::cout << "\nPerformance test: Running " << num_iterations << " iterations..." << std::endl;

		double total_time = 0.0;
		for (int i = 0; i < num_iterations; i++) {
			auto start = std::chrono::high_resolution_clock::now();
			segmentation = model.Extract(img);
			auto end = std::chrono::high_resolution_clock::now();

			std::chrono::duration<double, std::milli> duration = end - start;
			total_time += duration.count();

			std::cout << "Iteration " << (i+1) << "/" << num_iterations << ": " << duration.count() << " ms" << std::endl;
		}

		double avg_time = total_time / num_iterations;
		std::cout << "Average inference time: " << avg_time << " ms (" << (1000.0 / avg_time) << " FPS)" << std::endl;

	} catch (const std::exception& e) {
		std::cerr << "Exception caught: " << e.what() << std::endl;
	} catch (...) {
		std::cerr << "Unknown exception caught!" << std::endl;
	}
}