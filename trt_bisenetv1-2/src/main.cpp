/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-04-16 14:47:17
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-06-18 15:30:18
 * @FilePath: /Bisenet/trt_bisenetv1-2/src/main.cpp
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
#include "trt_bisenet.h"
#include <chrono>  // 用于性能测量

const float mean[] = {0.3257f, 0.3690f, 0.3223f}; // RGB 顺序
const float std_dev[]  = {0.2112f, 0.2148f, 0.2115f};

int main(int argc, char** argv)
{
	OnnxInitParam params;
	params.onnx_model_path = "../models/bisenet.onnx";
	params.rt_stream_path = "../models/";
	params.rt_model_name = "bisenet.engine";
	params.use_fp16 = true;
	params.gpu_id = 0;
	params.num_classes = 8;
	params.crop_size = 1024;
	params.max_shape = Shape(1, 3, 1024, 1024); // 设置最大网络输入大小，用于分配内存(显存)，根据自己项目需要设置

	BiSeNet model(params);
	// Step 1: 读取图像
	cv::Mat img = cv::imread("../images/1647074048.342873-1.png", cv::IMREAD_COLOR);
	if (img.empty()) {
		std::cerr << "Error: Could not read image file!" << std::endl;
		return -1;
	}
	std::cout << "Image size: " << img.cols << "x" << img.rows << std::endl;

	// Step 2: 确保图像尺寸符合模型要求
	cv::resize(img, img, cv::Size(params.crop_size, params.crop_size), 0, 0, cv::INTER_LINEAR); // 使用双线性插值调整尺寸

	// Step 3: 将BGR转换为RGB
	cv::cvtColor(img, img, cv::COLOR_BGR2RGB);

	// Step 4: 转换为浮点型 [0.0, 1.0]
	cv::Mat floatImage;
	img.convertTo(floatImage, CV_32F, 1.0 / 255.0);

	// Step 5: 拆分通道并标准化
	std::vector<cv::Mat> channels(3);
	cv::split(floatImage, channels);

	for (int i = 0; i < 3; ++i) {
		channels[i] = (channels[i] - mean[i]) / std_dev[i];
	}

	// Step 6: 合并通道（CHW 转换在后续处理中完成）
	cv::Mat normalizedImage;
	cv::merge(channels, normalizedImage);

	// Step 7: 可选 - 打印结果查看
	std::cout << "Normalized image size: " << normalizedImage.size() << std::endl;

	// Step 8: 模拟Python代码中的两次插值操作
	cv::Mat resizedImage;
	cv::resize(normalizedImage, resizedImage, cv::Size(params.crop_size, params.crop_size), 0, 0, cv::INTER_LINEAR); // 第一次插值
	cv::resize(resizedImage, resizedImage, cv::Size(org_size[1], org_size[0]), 0, 0, cv::INTER_LINEAR); // 第二次插值

	std::cout << "Starting extraction..." << std::endl;
	try {
		// 运行语义分割
		cv::Mat segmentation = model.Extract(resizedImage); // 使用调整后的图像进行推理
		std::cout << "Extraction completed. Result size: " << segmentation.cols << "x" << segmentation.rows << std::endl;

		// 将原始分割结果保存为图像文件
		std::string output_path = "../output/output_segmentation.png";
		bool success = cv::imwrite(output_path, segmentation);
		if (success) {
			std::cout << "Segmentation result saved to " << output_path << std::endl;
		} else {
			std::cerr << "Failed to save segmentation result to " << output_path << std::endl;
		}

		// 生成彩色分割图
		cv::Mat colored_segmentation = model.VisualizeSegmentation(segmentation);
		output_path = "../output/output_colored_segmentation.png";
		success = cv::imwrite(output_path, colored_segmentation);
		if (success) {
			std::cout << "Colored segmentation saved to " << output_path << std::endl;
		} else {
			std::cerr << "Failed to save colored segmentation to " << output_path << std::endl;
		}

		// 生成与原始图像混合的分割图
		cv::Mat blended_segmentation = model.VisualizeSegmentation(segmentation, img);
		output_path = "../output/output_blended_segmentation.png";
		success = cv::imwrite(output_path, blended_segmentation);
		if (success) {
			std::cout << "Blended segmentation saved to " << output_path << std::endl;
		} else {
			std::cerr << "Failed to save blended segmentation to " << output_path << std::endl;
		}

		// 测量性能：进行多次推理并计算平均时间
		const int num_iterations = 10;
		std::cout << "\nPerformance test: Running " << num_iterations << " iterations..." << std::endl;

		double total_time = 0.0;
		for (int i = 0; i < num_iterations; i++) {
			auto start = std::chrono::high_resolution_clock::now();
			segmentation = model.Extract(img);
			auto end = std::chrono::high_resolution_clock::now();

			std::chrono::duration<double, std::milli> duration = end - start;
			total_time += duration.count();

			std::cout << "Iteration " << (i+1) << "/" << num_iterations << ": " << duration.count() << " ms" << std::endl;
		}

		double avg_time = total_time / num_iterations;
		std::cout << "Average inference time: " << avg_time << " ms (" << (1000.0 / avg_time) << " FPS)" << std::endl;

	} catch (const std::exception& e) {
		std::cerr << "Exception caught: " << e.what() << std::endl;
	} catch (...) {
		std::cerr << "Unknown exception caught!" << std::endl;
	}
}