/*
 * @Author: hanshuangquan <EMAIL>
 * @Date: 2025-06-18 14:32:44
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-06-18 14:33:56
 * @FilePath: /Bisenet/trt_bisenetv1-2/src/trt_bisenet.cpp
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
#include "trt_bisenet.h"
#include "mat_transform.hpp"
#include "gpu_func.cuh"
#include <float.h>  // 用于FLT_MAX

BiSeNet::BiSeNet(const OnnxInitParam& params) : TRTOnnxBase(params)
{
}

cv::Mat BiSeNet::Extract(const cv::Mat& img)
{
	if (img.empty())
		return img;

	std::lock_guard<std::mutex> lock(mtx_);
	// 使用Python风格预处理以保持一致性
	PreProcessPythonStyle(img);
	Forward();

	// 使用Python风格后处理以保持一致性
	cv::Mat res = PostProcessPythonStyle();
	return std::move(res);
}

BiSeNet::~BiSeNet()
{
}

void BiSeNet::PreProcessCpu(const cv::Mat& img)
{
	cv::Mat img_tmp = img;

	ComposeMatLambda compose(std::vector<ComposeMatLambda::FuncionType>{
		LetterResize(cv::Size(_params.crop_size, _params.crop_size), cv::Scalar(114, 114, 114), 32),
		MatDivConstant(255),
		MatNormalize(mean_, std_),
		});

	cv::Mat sample_float = compose(img_tmp);
	input_shape_.Reshape(1, sample_float.channels(), sample_float.rows, sample_float.cols);
	output_shape_.Reshape(1, _params.num_classes, sample_float.rows, sample_float.cols);

	Tensor2VecMat tensor_2_mat;
	std::vector<cv::Mat> channels = tensor_2_mat(h_input_tensor_, input_shape_);
	cv::split(sample_float, channels);

	cudaMemcpy(d_input_tensor_, h_input_tensor_, input_shape_.count() * sizeof(float),
				cudaMemcpyHostToDevice);
}


void BiSeNet::ProProcessGPU(const cv::Mat& img)
{
	int src_h = img.rows;
	int src_w = img.cols;
	int channels = img.channels();

	float r = MIN(float(_params.crop_size) / src_h, float(_params.crop_size) / src_w);

	int dst_h = int(r * src_h);
	int dst_w = int(r * src_w);

	int pad_h = (_params.crop_size - dst_h) % stride_;
	int pad_w = (_params.crop_size - dst_w) % stride_;

	dst_h += pad_h;
	dst_w += pad_w;

	input_shape_.Reshape(1, channels, dst_h, dst_w);
	output_shape_.Reshape(1, _params.num_classes, dst_h, dst_w);

	biliresize_normalize(d_input_tensor_, channels, src_h, src_w, dst_h, dst_w,
		pad_h, pad_w, r, img.data, mean_.data(), std_.data());
}

void BiSeNet::PreProcessPythonStyle(const cv::Mat& img)
{
	// 模仿Python版本的预处理流程
	cv::Mat img_rgb;

	// Step 1: BGR转RGB (Python版本中cv2.imread读取后[:, :, ::-1])
	cv::cvtColor(img, img_rgb, cv::COLOR_BGR2RGB);

	// Step 2: 转换为浮点型并归一化到[0,1]
	cv::Mat img_float;
	img_rgb.convertTo(img_float, CV_32F, 1.0/255.0);

	// Step 3: 标准化 (减均值除标准差)
	std::vector<cv::Mat> channels(3);
	cv::split(img_float, channels);

	for (int i = 0; i < 3; ++i) {
		channels[i] = (channels[i] - mean_[i]) / std_[i];
	}

	cv::Mat normalized_img;
	cv::merge(channels, normalized_img);

	// Step 4: 获取原始尺寸并保存
	original_size_ = cv::Size(img.cols, img.rows);

	// Step 5: 计算新尺寸（32的倍数）
	int new_h = ((img.rows + 31) / 32) * 32;  // math.ceil(el / 32) * 32
	int new_w = ((img.cols + 31) / 32) * 32;
	cv::Size new_size(new_w, new_h);

	std::cout << "Original size: " << original_size_.width << "x" << original_size_.height << std::endl;
	std::cout << "New size: " << new_size.width << "x" << new_size.height << std::endl;

	// Step 6: 双线性插值resize
	cv::Mat resized_img;
	cv::resize(normalized_img, resized_img, new_size, 0, 0, cv::INTER_LINEAR);

	// Step 7: 设置输入和输出形状
	input_shape_.Reshape(1, 3, new_h, new_w);
	output_shape_.Reshape(1, _params.num_classes, new_h, new_w);

	// Step 8: 将数据复制到输入tensor (HWC -> CHW)
	std::vector<cv::Mat> input_channels(3);
	cv::split(resized_img, input_channels);

	// 将每个通道的数据复制到对应的内存位置
	int channel_size = new_h * new_w;
	for (int c = 0; c < 3; ++c) {
		memcpy(h_input_tensor_ + c * channel_size,
			   input_channels[c].data,
			   channel_size * sizeof(float));
	}

	// Step 9: 将数据从CPU复制到GPU
	cudaMemcpy(d_input_tensor_, h_input_tensor_,
			   input_shape_.count() * sizeof(float),
			   cudaMemcpyHostToDevice);
}

cv::Mat BiSeNet::PostProcessCpu()
{
	int num = output_shape_.num();
	int channels = output_shape_.channels();
	int height = output_shape_.height();
	int width = output_shape_.width();
	int count = output_shape_.count();

	cudaMemcpy(h_output_tensor_, d_output_tensor_,
		count * sizeof(float), cudaMemcpyDeviceToHost);

	cv::Mat res = cv::Mat::zeros(height, width, CV_8UC1);
	for (int row = 0; row < height; row++)
	{
		for (int col = 0; col < width; col++)
		{
			vector<float> vec;
			for (int c = 0; c < channels; c++)
			{
				int index = row * width + col + c * height * width;
				float val = h_output_tensor_[index];
				vec.push_back(val);
			}

			int idx = findMaxIdx(vec);
			if (idx == -1)
				continue;
			res.at<uchar>(row, col) = uchar(idx);
		}
	}

	return std::move(res);
}

cv::Mat BiSeNet::PostProcessGpu()
{
	int num = output_shape_.num();
	int channels = output_shape_.channels();
	int height = output_shape_.height();
	int width = output_shape_.width();

	std::cout << "PostProcessGpu - Output shape: " << num << "," << channels << "," << height << "," << width << std::endl;

	unsigned char* cpu_dst;
	cudaHostAlloc((void**)&cpu_dst, height * width * sizeof(unsigned char), cudaHostAllocDefault);

	// 检查CUDA错误
	cudaError_t err = cudaGetLastError();
	if (err != cudaSuccess) {
		std::cerr << "CUDA Error after cudaHostAlloc: " << cudaGetErrorString(err) << std::endl;
	}

	//==> segmentation(output_tensor_, channels, height, width, cpu_dst);
	segmentation(d_output_tensor_, channels, height, width, cpu_dst);

	// 检查CUDA错误
	err = cudaGetLastError();
	if (err != cudaSuccess) {
		std::cerr << "CUDA Error after segmentation: " << cudaGetErrorString(err) << std::endl;
	}

	// 创建一个新的Mat并复制数据，而不是使用外部数据
	cv::Mat res(height, width, CV_8UC1);
	memcpy(res.data, cpu_dst, height * width * sizeof(unsigned char));

	cudaFreeHost(cpu_dst);
	return res;
}

cv::Mat BiSeNet::PostProcessPythonStyle()
{
	// 模仿Python版本的后处理流程
	int num = output_shape_.num();
	int channels = output_shape_.channels();
	int height = output_shape_.height();
	int width = output_shape_.width();
	int count = output_shape_.count();

	std::cout << "PostProcessPythonStyle - Output shape: " << num << "," << channels << "," << height << "," << width << std::endl;
	std::cout << "Original size for resize: " << original_size_.width << "x" << original_size_.height << std::endl;

	// Step 1: 将GPU数据复制到CPU
	cudaMemcpy(h_output_tensor_, d_output_tensor_,
		count * sizeof(float), cudaMemcpyDeviceToHost);

	// Step 2: 创建输出tensor的Mat表示 (CHW格式)
	std::vector<cv::Mat> output_channels(channels);
	for (int c = 0; c < channels; ++c) {
		output_channels[c] = cv::Mat(height, width, CV_32F,
			h_output_tensor_ + c * height * width);
	}

	// Step 3: 找到每个像素的最大值索引 (argmax)
	cv::Mat result = cv::Mat::zeros(height, width, CV_8UC1);
	for (int y = 0; y < height; y++) {
		for (int x = 0; x < width; x++) {
			float max_val = -FLT_MAX;
			int max_idx = 0;
			for (int c = 0; c < channels; c++) {
				float val = output_channels[c].at<float>(y, x);
				if (val > max_val) {
					max_val = val;
					max_idx = c;
				}
			}
			result.at<uchar>(y, x) = static_cast<uchar>(max_idx);
		}
	}

	// Step 4: 将结果resize回原始尺寸 (模仿Python版本的F.interpolate)
	cv::Mat final_result;
	if (original_size_.width != width || original_size_.height != height) {
		cv::resize(result, final_result, original_size_, 0, 0, cv::INTER_LINEAR);
		std::cout << "Resized result from " << width << "x" << height
				  << " to " << original_size_.width << "x" << original_size_.height << std::endl;
	} else {
		final_result = result;
	}

	return final_result;
}

void BiSeNet::softmax(vector<float>& vec)
{
	float tol = 0.0;
	for (int i = 0; i < vec.size(); i++)
	{
		vec[i] = exp(vec[i]);
		tol += vec[i];
	}

	for (int i = 0; i < vec.size(); i++)
		vec[i] = vec[i] / tol;
}

int BiSeNet::findMaxIdx(const vector<float>& vec)
{
	if (vec.empty())
		return -1;
	auto pos = max_element(vec.begin(), vec.end());
	return std::distance(vec.begin(), pos);
}

cv::Mat BiSeNet::VisualizeSegmentation(const cv::Mat& segmentation, const cv::Mat& original_img)
{
	// 定义类别颜色映射（最多支持10个类别，可以根据需要扩展）
	std::vector<cv::Vec3b> colors = {
		cv::Vec3b(0, 0, 0),       // 类别0: 黑色 (背景)
		cv::Vec3b(128, 0, 0),     // 类别1: 深红色
		cv::Vec3b(0, 128, 0),     // 类别2: 深绿色
		cv::Vec3b(128, 128, 0),   // 类别3: 深黄色
		cv::Vec3b(0, 0, 128),     // 类别4: 深蓝色
		cv::Vec3b(128, 0, 128),   // 类别5: 深紫色
		cv::Vec3b(0, 128, 128),   // 类别6: 深青色
		cv::Vec3b(128, 128, 128), // 类别7: 灰色
		cv::Vec3b(64, 0, 0),      // 类别8: 暗红色
		cv::Vec3b(192, 0, 0)      // 类别9: 亮红色
	};

	// 创建彩色分割图
	cv::Mat colored_segmentation = cv::Mat::zeros(segmentation.size(), CV_8UC3);

	// 为每个像素分配颜色
	for (int y = 0; y < segmentation.rows; y++) {
		for (int x = 0; x < segmentation.cols; x++) {
			unsigned char class_id = segmentation.at<unsigned char>(y, x);
			// 确保类别索引在颜色数组范围内
			if (class_id < colors.size()) {
				colored_segmentation.at<cv::Vec3b>(y, x) = colors[class_id];
			}
		}
	}

	// 如果提供了原始图像，则将分割结果与原始图像混合
	if (!original_img.empty()) {
		cv::Mat resized_original;
		// 确保原始图像与分割结果大小相同
		cv::resize(original_img, resized_original, segmentation.size());

		// 混合原始图像和分割结果（半透明效果）
		cv::Mat blended;
		cv::addWeighted(resized_original, 0.7, colored_segmentation, 0.3, 0, blended);
		return blended;
	}

	return colored_segmentation;
}