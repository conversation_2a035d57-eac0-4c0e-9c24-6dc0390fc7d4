#ifndef TRT_BISENET_H_
#define TRT_BISENET_H_

#include "trt_onnx_base.h"
#include <mutex>

#define MIN(x, y) (x) < (y) ? (x) : (y)

using namespace std;

class BiSeNet : public TRTOnnxBase
{
public:
	BiSeNet() = delete;
	BiSeNet(const OnnxInitParam& params);

	virtual ~BiSeNet();

	cv::Mat Extract(const cv::Mat& img);

	// 可视化分割结果，为不同类别分配不同的颜色
	cv::Mat VisualizeSegmentation(const cv::Mat& segmentation, const cv::Mat& original_img = cv::Mat());

private:
	// cpu预处理
	void PreProcessCpu(const cv::Mat& img);
	// gpu预处理
	void ProProcessGPU(const cv::Mat& img);
	// Python风格预处理（与Python版本保持一致）
	void PreProcessPythonStyle(const cv::Mat& img);

	// cpu后处理
	cv::Mat PostProcessCpu();
	// gpu后处理
	cv::Mat PostProcessGpu();
	// Python风格后处理（与Python版本保持一致）
	cv::Mat PostProcessPythonStyle();

	// softmax函数
	static void softmax(vector<float>& vec);
	static int findMaxIdx(const vector<float>& vec);

private:
	// int crop_size_ = 1024;
	int stride_ = 32;

	std::vector<float> mean_{ 0.3257, 0.3690, 0.3223 };  // RGB 顺序，与Python版本一致
	std::vector<float> std_{ 0.2112, 0.2148, 0.2115 };

	cv::Size original_size_;  // 保存原始图像尺寸，用于后处理resize
	std::mutex mtx_;
};

#endif